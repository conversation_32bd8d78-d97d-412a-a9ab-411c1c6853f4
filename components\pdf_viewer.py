import asyncio
import fitz
import flet as ft
import os
from utils.pdf_processor import PDFProcessor, PDF_IMAGE_SCALE

class PDFViewerComponent:
    def __init__(self, page, state):
        self.page = page
        self.state = state
        # 移除重复的zoom_level，统一使用state.zoom_level
        self.view = ft.Column(expand=True, spacing=0, scroll=ft.ScrollMode.AUTO)
        self.bbox_containers = []  # 存储当前页的bbox容器
        self.pdf_processor = PDFProcessor()
        self.processing_mode = False

    def create(self):
        # 注册UI控制回调
        self.state.add_ui_control_callback(self._set_processing_mode)
        
        # 创建导航按钮引用
        self.prev_btn = ft.IconButton(ft.Icons.NAVIGATE_BEFORE, on_click=self.prev_page)
        self.next_btn = ft.IconButton(ft.Icons.NAVIGATE_NEXT, on_click=self.next_page)
        self.zoom_out_btn = ft.IconButton(ft.Icons.ZOOM_OUT, on_click=self.zoom_out)
        self.zoom_in_btn = ft.IconButton(ft.Icons.ZOOM_IN, on_click=self.zoom_in)
        
        return ft.Container(
            content=ft.Column(
                expand=False,
                alignment=ft.MainAxisAlignment.START,
                horizontal_alignment=ft.CrossAxisAlignment.START,
                controls=[
                    ft.Row(
                        controls=[
                            self.prev_btn,
                            self.state.page_counter,
                            self.next_btn,
                            self.zoom_out_btn,
                            self.zoom_in_btn,
                        ],
                        alignment=ft.MainAxisAlignment.CENTER
                    ),
                    ft.Container(
            content=self.view,
            expand=True,
            border=ft.border.all(1, ft.Colors.GREY_400),
            padding=0,
            border_radius=5,
            margin=0
        )
                ]
            ),
            expand=True,
            alignment=ft.alignment.top_left
        )
    
    async def load_pdf(self, pdf_path):
        # 强制重置所有相关状态
        self.state.current_pdf = pdf_path
        self.state.reset_all_selections()
        
        # 重新打开PDF文档
        self.state.pdf_doc = await asyncio.to_thread(fitz.open, pdf_path)
        self.state.total_pages = self.state.pdf_doc.page_count
        
        # 强制清除视图控件和矩形框
        self.view.controls.clear()
        self.bbox_containers = []
        
        # 确保使用最新的JSON数据
        if hasattr(self.state, 'json_data') and self.state.json_data:
            self.render_page(0)
            self.draw_bboxes(0)
        else:
            # 如果没有JSON数据，只渲染页面
            self.render_page(0)

    def render_page(self, page_index):
        self.view.controls.clear()
        self.bbox_containers = []
        
        if not self.state.pdf_doc or page_index >= self.state.total_pages:
            return
        
        # 获取预生成的图片路径
        img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_index)
        
        if not os.path.exists(img_path):
            # 如果图片不存在，创建它
            page = self.state.pdf_doc.load_page(page_index)
            zoom = PDF_IMAGE_SCALE  # 使用全局定义的缩放比例
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            pix.save(img_path)
        
        # 获取PDF页面尺寸
        page = self.state.pdf_doc.load_page(page_index)
        page_width = page.rect.width
        page_height = page.rect.height
        
        # 让图片自适应容器宽度
        display_scale = self.state.zoom_level

        # 用于bbox坐标转换的缩放因子
        # 由于图片会自适应宽度，这个缩放因子需要动态计算
        # 暂时使用display_scale，后续可能需要调整
        total_scale = display_scale

        # 创建图片控件 - 宽度适应容器，高度按比例
        image = ft.Image(
            src=img_path,
            key=f"img_{page_index}_{self.state.zoom_level}",
            fit=ft.ImageFit.FIT_WIDTH,  # 适应宽度，高度按比例调整
            gapless_playback=True,
            # 不设置固定的width和height，让图片自适应
        )

        # 创建Stack布局 - 也不设置固定尺寸，让它自适应内容
        stack = ft.Stack(
            controls=[image],
            # 不设置固定的width和height，让Stack自适应图片尺寸
        )
        
        # 创建容器 - 让容器自适应内容
        center_container = ft.Container(
            content=stack,
            expand=True,
            padding=5,  # 使用较小的padding
            alignment=ft.alignment.top_center,  # 图片在容器中水平居中
        )
        
        self.view.controls.append(center_container)
        
        # 更新页码显示
        self.state.page_counter.value = f"{page_index + 1}/{self.state.total_pages}"
        self.page.update()
        
        # 绘制当前页的bbox
        self.draw_bboxes(page_index)

    def draw_bboxes(self, page_index):
        """绘制当前页的bbox区域"""
        if not hasattr(self.state, 'json_data') or not self.state.json_data:
            return
        
        # 确保使用最新的JSON数据
        pdf_info = self.state.json_data.get('pdf_info', [])
        if page_index >= len(pdf_info):
            return
        
        page_obj = pdf_info[page_index]
        blocks = page_obj.get('para_blocks', [])
        
        # 获取当前Stack控件
        if not self.view.controls or not isinstance(self.view.controls[0], ft.Container):
            return
        
        center_container = self.view.controls[0]
        if not center_container.content or not isinstance(center_container.content, ft.Stack):
            return
        
        stack = center_container.content
        
        # 清除现有bbox容器
        if len(stack.controls) > 1:
            stack.controls = stack.controls[:1]  # 保留图片控件
        
        # 获取预生成图片的实际像素尺寸
        img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_index)
        try:
            from PIL import Image as PILImage
            with PILImage.open(img_path) as pil_img:
                image_pixel_width, image_pixel_height = pil_img.size
        except Exception as e:
            print(f"DEBUG: 无法读取图片尺寸: {e}")
            # 回退到PDF尺寸 × 预生成缩放
            page = self.state.pdf_doc.load_page(page_index)
            image_pixel_width = page.rect.width * PDF_IMAGE_SCALE
            image_pixel_height = page.rect.height * PDF_IMAGE_SCALE
        
        # 计算矩形框的缩放比例
        display_scale = self.state.zoom_level

        # 动态计算容器可用宽度
        # 获取页面宽度，减去左侧文件树和各种边距
        page_width = self.page.width if self.page.width else 1200  # 默认页面宽度
        file_tree_width = 200  # 左侧文件树宽度
        json_editor_width = 400  # 右侧JSON编辑器大概宽度
        margins_and_padding = 50  # 各种边距和padding

        estimated_container_width = page_width - file_tree_width - json_editor_width - margins_and_padding

        # 确保最小宽度
        estimated_container_width = max(estimated_container_width, 300)

        # 计算图片在容器中的实际显示缩放比例
        # 由于图片使用FIT_WIDTH，显示宽度 = 容器宽度
        # 显示缩放比例 = 容器宽度 / 图片像素宽度
        image_display_scale = estimated_container_width / image_pixel_width

        # 矩形框的总缩放比例 = 图片显示缩放 × 用户缩放
        total_scale = image_display_scale * display_scale
        
        # 调试信息
        print(f"DEBUG: 图片像素尺寸 {image_pixel_width}x{image_pixel_height}")
        print(f"DEBUG: 页面总宽度 {page_width}")
        print(f"DEBUG: 计算后容器宽度 {estimated_container_width}")
        print(f"DEBUG: 图片显示缩放 {image_display_scale:.3f}")
        print(f"DEBUG: 用户缩放级别 {display_scale}")
        print(f"DEBUG: 矩形框总缩放 total_scale={total_scale:.3f}")

        # 添加新的bbox容器
        for block_idx, block in enumerate(blocks):
            bbox = block.get('bbox', [])
            if len(bbox) != 4:
                continue

            x0, y0, x1, y1 = bbox
            
            # 坐标转换 - 考虑容器padding和图片居中
            # PyMuPDF坐标系: 原点在左上角，y轴向下

            # 基础坐标转换
            left = x0 * total_scale
            top = y0 * total_scale
            width = (x1 - x0) * total_scale
            height = (y1 - y0) * total_scale

            # 添加容器padding偏移
            container_padding = 5  # 与center_container的padding一致
            left += container_padding
            top += container_padding

            # 调试信息
            print(f"DEBUG: 原始bbox={bbox}")
            print(f"DEBUG: 缩放后坐标 left={left:.1f}, top={top:.1f}, width={width:.1f}, height={height:.1f}")
            
            # 确定边框颜色
            is_selected = (self.state.selected_page == page_index and 
                          self.state.selected_block == block_idx)
            border_color = ft.Colors.BLUE if is_selected else ft.Colors.RED
            
            # 创建bbox容器
            bbox_container = ft.Container(
                left=left,
                top=top,
                width=width,
                height=height,
                border=ft.border.all(2, border_color),
                bgcolor=ft.Colors.TRANSPARENT,
                expand=False,
                on_click=lambda e, idx=block_idx: self.on_bbox_click(e, page_index, idx)
            )
            
            stack.controls.append(bbox_container)
        
        self.page.update()

    def on_bbox_click(self, e, page_index, block_idx):
        """处理bbox区域点击事件"""
        if self.processing_mode:
            return  # 处理中禁用点击
            
        # 使用状态setter方法触发监听器
        self.state.set_selected_page(page_index)
        self.state.set_selected_block(block_idx)
        if hasattr(self.state, 'status_manager') and self.state.status_manager:
            self.page.run_task(
                self.state.status_manager.set_user_action_status,
                f"已选择: 第{page_index + 1}页, 第{block_idx + 1}个内容块"
            )
        # 更新JSON编辑器选中状态
        if hasattr(self.state, 'json_editor') and self.state.json_editor:
            self.state.json_editor.highlight_selected_block()
        # 重新绘制以更新颜色
        self.draw_bboxes(page_index)

    def _set_processing_mode(self, processing: bool):
        """设置处理模式，禁用/启用导航和缩放按钮"""
        self.processing_mode = processing
        
        # 禁用/启用导航和缩放按钮
        if hasattr(self, 'prev_btn'):
            self.prev_btn.disabled = not processing
        if hasattr(self, 'next_btn'):
            self.next_btn.disabled = not processing
        if hasattr(self, 'zoom_out_btn'):
            self.zoom_out_btn.disabled = not processing
        if hasattr(self, 'zoom_in_btn'):
            self.zoom_in_btn.disabled = not processing
            
        self.page.update()

    def prev_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index > 0:
            self.state.current_page_index -= 1
            self.render_page(self.state.current_page_index)
    
    def zoom_out(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level > 1.0:  # 统一使用state.zoom_level
            self.state.zoom_level -= 0.5
            self.render_page(self.state.current_page_index)

    def zoom_in(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level < 5.0:  # 统一使用state.zoom_level
            self.state.zoom_level += 0.5
            self.render_page(self.state.current_page_index)

    def next_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index < self.state.total_pages - 1:
            self.state.current_page_index += 1
            self.render_page(self.state.current_page_index)

