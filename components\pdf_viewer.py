import asyncio
import fitz
import flet as ft
import os
from utils.pdf_processor import PDFProcessor

class PDFViewerComponent:
    def __init__(self, page, state):
        self.page = page
        self.state = state
        # 移除重复的zoom_level，统一使用state.zoom_level
        self.view = ft.Column(expand=True, spacing=0, scroll=ft.ScrollMode.AUTO)
        self.bbox_containers = []  # 存储当前页的bbox容器
        self.pdf_processor = PDFProcessor()
        self.processing_mode = False

    def create(self):
        # 注册UI控制回调
        self.state.add_ui_control_callback(self._set_processing_mode)
        
        # 创建导航按钮引用
        self.prev_btn = ft.IconButton(ft.Icons.NAVIGATE_BEFORE, on_click=self.prev_page)
        self.next_btn = ft.IconButton(ft.Icons.NAVIGATE_NEXT, on_click=self.next_page)
        self.zoom_out_btn = ft.IconButton(ft.Icons.ZOOM_OUT, on_click=self.zoom_out)
        self.zoom_in_btn = ft.IconButton(ft.Icons.ZOOM_IN, on_click=self.zoom_in)
        
        return ft.Container(
            content=ft.Column(
                expand=False,
                alignment=ft.MainAxisAlignment.START,
                horizontal_alignment=ft.CrossAxisAlignment.START,
                controls=[
                    ft.Row(
                        controls=[
                            self.prev_btn,
                            self.state.page_counter,
                            self.next_btn,
                            self.zoom_out_btn,
                            self.zoom_in_btn,
                        ],
                        alignment=ft.MainAxisAlignment.CENTER
                    ),
                    ft.Container(
            content=self.view,
            expand=True,
            border=ft.border.all(1, ft.Colors.GREY_400),
            padding=0,
            border_radius=5,
            margin=0
        )
                ]
            ),
            expand=True,
            alignment=ft.alignment.top_left
        )
    
    async def load_pdf(self, pdf_path):
        # 强制重置所有相关状态
        self.state.current_pdf = pdf_path
        self.state.reset_all_selections()
        
        # 重新打开PDF文档
        self.state.pdf_doc = await asyncio.to_thread(fitz.open, pdf_path)
        self.state.total_pages = self.state.pdf_doc.page_count
        
        # 强制清除视图控件和矩形框
        self.view.controls.clear()
        self.bbox_containers = []
        
        # 确保使用最新的JSON数据
        if hasattr(self.state, 'json_data') and self.state.json_data:
            self.render_page(0)
            self.draw_bboxes(0)
        else:
            # 如果没有JSON数据，只渲染页面
            self.render_page(0)

    def render_page(self, page_index):
        self.view.controls.clear()
        self.bbox_containers = []
        
        if not self.state.pdf_doc or page_index >= self.state.total_pages:
            return
        
        # 获取预生成的图片路径
        img_path = self.pdf_processor.get_page_image_path(self.state.current_pdf, page_index)
        
        if not os.path.exists(img_path):
            # 如果图片不存在，创建它
            page = self.state.pdf_doc.load_page(page_index)
            zoom = 2.0  # 使用高DPI
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            pix.save(img_path)
        
        # 获取PDF页面尺寸
        page = self.state.pdf_doc.load_page(page_index)
        page_width = page.rect.width
        page_height = page.rect.height
        
        # 获取容器可用宽度
        # 这里使用一个合理的默认宽度，后续可以改为动态获取
        container_available_width = 600  # 可以根据实际情况调整

        # 计算适应宽度的基础缩放比例
        base_width_scale = container_available_width / page_width

        # 应用用户的缩放级别
        final_scale = base_width_scale * self.state.zoom_level

        # 计算最终显示尺寸（宽度适应容器，高度按比例）
        final_width = page_width * final_scale
        final_height = page_height * final_scale

        # 用于bbox坐标转换的总缩放因子
        total_scale = final_scale

        # 创建图片控件 - 宽度适应容器，高度按比例
        image = ft.Image(
            src=img_path,
            key=f"img_{page_index}_{self.state.zoom_level}",
            fit=ft.ImageFit.FIT_WIDTH,  # 适应宽度，高度按比例调整
            gapless_playback=True,
            width=final_width,
            height=final_height
        )
        
        # 创建Stack布局
        stack = ft.Stack(
            controls=[image],
            width=final_width,
            height=final_height
        )
        
        # 创建容器 - 使用最小padding
        center_container = ft.Container(
            content=stack,
            expand=True,
            padding=5,  # 使用较小的padding
            alignment=ft.alignment.top_left,  # 保持左上角对齐
        )
        
        self.view.controls.append(center_container)
        
        # 更新页码显示
        self.state.page_counter.value = f"{page_index + 1}/{self.state.total_pages}"
        self.page.update()
        
        # 绘制当前页的bbox
        self.draw_bboxes(page_index)

    def draw_bboxes(self, page_index):
        """绘制当前页的bbox区域"""
        if not hasattr(self.state, 'json_data') or not self.state.json_data:
            return
        
        # 确保使用最新的JSON数据
        pdf_info = self.state.json_data.get('pdf_info', [])
        if page_index >= len(pdf_info):
            return
        
        page_obj = pdf_info[page_index]
        blocks = page_obj.get('para_blocks', [])
        
        # 获取当前Stack控件
        if not self.view.controls or not isinstance(self.view.controls[0], ft.Container):
            return
        
        center_container = self.view.controls[0]
        if not center_container.content or not isinstance(center_container.content, ft.Stack):
            return
        
        stack = center_container.content
        
        # 清除现有bbox容器
        if len(stack.controls) > 1:
            stack.controls = stack.controls[:1]  # 保留图片控件
        
        # 获取PDF页面信息用于坐标转换
        page = self.state.pdf_doc.load_page(page_index)
        pdf_page_width = page.rect.width
        pdf_page_height = page.rect.height
        
        # 统一缩放计算
        display_scale = self.state.zoom_level
        pre_scale = 2.0  # 图片预生成时的固定缩放因子
        total_scale = pre_scale * display_scale
        
        # 调试信息
        print(f"DEBUG: PDF页面尺寸 {pdf_page_width}x{pdf_page_height}")
        print(f"DEBUG: 容器可用宽度 {container_available_width}")
        print(f"DEBUG: 基础宽度缩放 {base_width_scale:.3f}")
        print(f"DEBUG: 用户缩放级别 {self.state.zoom_level}")
        print(f"DEBUG: 最终缩放因子 {total_scale:.3f}")
        print(f"DEBUG: 最终显示尺寸 {final_width:.1f}x{final_height:.1f}")

        # 添加新的bbox容器
        for block_idx, block in enumerate(blocks):
            bbox = block.get('bbox', [])
            if len(bbox) != 4:
                continue

            x0, y0, x1, y1 = bbox
            
            # 最简单的坐标转换
            # PyMuPDF坐标系: 原点在左上角，y轴向下
            # 直接按比例缩放，不添加任何偏移
            left = x0 * total_scale
            top = y0 * total_scale
            width = (x1 - x0) * total_scale
            height = (y1 - y0) * total_scale

            # 调试信息
            print(f"DEBUG: 原始bbox={bbox}")
            print(f"DEBUG: 缩放后坐标 left={left}, top={top}, width={width}, height={height}")
            
            # 确定边框颜色
            is_selected = (self.state.selected_page == page_index and 
                          self.state.selected_block == block_idx)
            border_color = ft.Colors.BLUE if is_selected else ft.Colors.RED
            
            # 创建bbox容器
            bbox_container = ft.Container(
                left=left,
                top=top,
                width=width,
                height=height,
                border=ft.border.all(2, border_color),
                bgcolor=ft.Colors.TRANSPARENT,
                expand=False,
                on_click=lambda e, idx=block_idx: self.on_bbox_click(e, page_index, idx)
            )
            
            stack.controls.append(bbox_container)
        
        self.page.update()

    def on_bbox_click(self, e, page_index, block_idx):
        """处理bbox区域点击事件"""
        if self.processing_mode:
            return  # 处理中禁用点击
            
        # 使用状态setter方法触发监听器
        self.state.set_selected_page(page_index)
        self.state.set_selected_block(block_idx)
        if hasattr(self.state, 'status_manager') and self.state.status_manager:
            self.page.run_task(
                self.state.status_manager.set_user_action_status,
                f"已选择: 第{page_index + 1}页, 第{block_idx + 1}个内容块"
            )
        # 更新JSON编辑器选中状态
        if hasattr(self.state, 'json_editor') and self.state.json_editor:
            self.state.json_editor.highlight_selected_block()
        # 重新绘制以更新颜色
        self.draw_bboxes(page_index)

    def _set_processing_mode(self, processing: bool):
        """设置处理模式，禁用/启用导航和缩放按钮"""
        self.processing_mode = processing
        
        # 禁用/启用导航和缩放按钮
        if hasattr(self, 'prev_btn'):
            self.prev_btn.disabled = not processing
        if hasattr(self, 'next_btn'):
            self.next_btn.disabled = not processing
        if hasattr(self, 'zoom_out_btn'):
            self.zoom_out_btn.disabled = not processing
        if hasattr(self, 'zoom_in_btn'):
            self.zoom_in_btn.disabled = not processing
            
        self.page.update()

    def prev_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index > 0:
            self.state.current_page_index -= 1
            self.render_page(self.state.current_page_index)
    
    def zoom_out(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level > 1.0:  # 统一使用state.zoom_level
            self.state.zoom_level -= 0.5
            self.render_page(self.state.current_page_index)

    def zoom_in(self, e):
        if self.processing_mode:
            return  # 处理中禁用缩放
        if self.state.zoom_level < 5.0:  # 统一使用state.zoom_level
            self.state.zoom_level += 0.5
            self.render_page(self.state.current_page_index)

    def next_page(self, e):
        if self.processing_mode:
            return  # 处理中禁用翻页
        if self.state.current_page_index < self.state.total_pages - 1:
            self.state.current_page_index += 1
            self.render_page(self.state.current_page_index)

