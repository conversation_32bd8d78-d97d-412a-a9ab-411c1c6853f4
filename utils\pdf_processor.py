import os
import fitz
import asyncio
import flet as ft
import threading
from utils.status_manager import StatusManager

class PDFProcessor:
    def __init__(self):
        self.cancel_event = threading.Event()
        self.status_manager = None
        
    async def process_pdf_images(self, pdf_path, status_manager, on_complete=None):
        """处理PDF图片预生成，带进度条"""
        if not os.path.exists(pdf_path):
            return False
            
        # 获取PDF文件夹路径
        pdf_dir = os.path.dirname(pdf_path)
        images_dir = os.path.join(pdf_dir, "images")
        
        # 确保images目录存在
        if not os.path.exists(images_dir):
            os.makedirs(images_dir)
            
        # 打开PDF文件
        try:
            doc = fitz.open(pdf_path)
            total_pages = doc.page_count
            
            # 检查哪些页面需要处理
            pages_to_process = []
            for i in range(total_pages):
                img_path = os.path.join(images_dir, f"page_{i+1}.png")
                if not os.path.exists(img_path):
                    pages_to_process.append(i)
            
            if not pages_to_process:
                doc.close()
                if on_complete:
                    await on_complete()
                return True
                
            # 显示进度条
            print(f"DEBUG: 需要处理 {len(pages_to_process)} 页，总页面 {total_pages}")
            
            # 直接使用传入的状态管理器
            if status_manager:
                self.status_manager = status_manager
                await self.status_manager.set_processing_status(
                    f"正在处理PDF图片... 0/{len(pages_to_process)}", 0
                )
                print("DEBUG: 状态栏已设置为初始状态")
            else:
                print("DEBUG: 警告 - 状态管理器未提供，状态栏不会更新")
            
            # 使用asyncio.to_thread实现非阻塞处理
            completed = 0
            total_to_process = len(pages_to_process)
            
            for page_idx in pages_to_process:
                if self.cancel_event.is_set():
                    break
                try:
                    # 非阻塞执行图片生成
                    await asyncio.to_thread(self._generate_page_image, doc, page_idx, images_dir)
                    completed += 1
                    
                    # 更新进度并给UI更新机会
                    await self._update_progress(completed, total_to_process)
                    await asyncio.sleep(0)  # 让出控制权给UI
                    
                except Exception as e:
                    print(f"处理第{page_idx+1}页失败: {e}")
            
            doc.close()
            await self._close_progress_dialog()
            
            if not self.cancel_event.is_set() and on_complete:
                await on_complete()
                
            return not self.cancel_event.is_set()
            
        except Exception as e:
            print(f"处理PDF图片失败: {e}")
            import traceback
            traceback.print_exc()
            await self._close_progress_dialog()
            return False
    
    def _generate_page_image(self, doc, page_idx, images_dir):
        """生成单页图片"""
        try:
            page = doc.load_page(page_idx)
            # 使用固定的高DPI设置
            zoom = 2.0  # 200% DPI
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            
            img_path = os.path.join(images_dir, f"page_{page_idx+1}.png")
            pix.save(img_path)
            
            return True
        except Exception as e:
            print(f"生成第{page_idx+1}页图片失败: {e}")
            return False
    

    
    async def _update_progress(self, completed, total):
        """更新进度"""
        if self.status_manager:
            progress_percent = int((completed / total) * 100)
            print(f"DEBUG: 更新进度 {completed}/{total} ({progress_percent}%)")
            await self.status_manager.update_processing_progress(completed, total)
        else:
            print(f"DEBUG: 警告 - 状态管理器未初始化，无法更新进度 {completed}/{total}")
    
    async def _close_progress_dialog(self):
        """隐藏进度条"""
        print("DEBUG: 隐藏进度条")
        if self.status_manager:
            await self.status_manager.complete_processing()
            print("DEBUG: 进度条已隐藏")
        else:
            print("DEBUG: 警告 - 状态管理器未初始化，无法隐藏进度条")
    
    def _cancel_processing(self, e):
        """取消处理"""
        self.cancel_event.set()
        # 取消按钮不再需要，因为进度条在toolbar中
    
    def get_page_image_path(self, pdf_path, page_index):
        """获取页面图片路径"""
        pdf_dir = os.path.dirname(pdf_path)
        images_dir = os.path.join(pdf_dir, "images")
        return os.path.join(images_dir, f"page_{page_index+1}.png")
    
    def check_images_exist(self, pdf_path):
        """检查所有页面图片是否存在"""
        if not os.path.exists(pdf_path):
            return False
            
        try:
            doc = fitz.open(pdf_path)
            total_pages = doc.page_count
            doc.close()
            
            pdf_dir = os.path.dirname(pdf_path)
            images_dir = os.path.join(pdf_dir, "images")
            
            for i in range(total_pages):
                img_path = os.path.join(images_dir, f"page_{i+1}.png")
                if not os.path.exists(img_path):
                    return False
            
            return True
        except:
            return False